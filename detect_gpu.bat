@echo off
setlocal enabledelayedexpansion

echo.
echo ========================================
echo ?? GPU/CPU Detection for AI Meeting Notes
echo ========================================
echo.

:: Check if Python is installed
echo ?? Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ? Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

:: Get Python version
for /f "tokens=2" %%i in ('python --version') do set PYTHON_VERSION=%%i
echo ? Python !PYTHON_VERSION! found

echo.
echo ?? Detecting GPU and CUDA capabilities...
echo.

:: Create temporary Python script for detection
echo import torch > temp_gpu_detect.py
echo import sys >> temp_gpu_detect.py
echo. >> temp_gpu_detect.py
echo print("???  System Information:") >> temp_gpu_detect.py
echo print("=" * 40) >> temp_gpu_detect.py
echo print(f"PyTorch Version: {torch.__version__}") >> temp_gpu_detect.py
echo print(f"CUDA Available: {torch.cuda.is_available()}") >> temp_gpu_detect.py
echo print() >> temp_gpu_detect.py
echo. >> temp_gpu_detect.py
echo if torch.cuda.is_available(): >> temp_gpu_detect.py
echo     print("?? NVIDIA GPU DETECTED!") >> temp_gpu_detect.py
echo     print("=" * 40) >> temp_gpu_detect.py
echo     print(f"CUDA Version: {torch.version.cuda}") >> temp_gpu_detect.py
echo     print(f"cuDNN Version: {torch.backends.cudnn.version()}") >> temp_gpu_detect.py
echo     print(f"GPU Count: {torch.cuda.device_count()}") >> temp_gpu_detect.py
echo     print() >> temp_gpu_detect.py
echo     for i in range(torch.cuda.device_count()): >> temp_gpu_detect.py
echo         props = torch.cuda.get_device_properties(i) >> temp_gpu_detect.py
echo         memory_gb = props.total_memory / (1024**3) >> temp_gpu_detect.py
echo         print(f"GPU {i}: {props.name}") >> temp_gpu_detect.py
echo         print(f"  Memory: {memory_gb:.1f} GB") >> temp_gpu_detect.py
echo         print(f"  Compute Capability: {props.major}.{props.minor}") >> temp_gpu_detect.py
echo         print() >> temp_gpu_detect.py
echo     print("? TRANSCRIPTION PERFORMANCE:") >> temp_gpu_detect.py
echo     print("  Expected Speed: 5-10x faster than CPU") >> temp_gpu_detect.py
echo     print("  Optimal for: Real-time transcription") >> temp_gpu_detect.py
echo     print("  Recommended Models: large-v3, large-v2, medium") >> temp_gpu_detect.py
echo     print() >> temp_gpu_detect.py
echo     print("? Your system is optimized for fast AI transcription!") >> temp_gpu_detect.py
echo else: >> temp_gpu_detect.py
echo     print("???  CPU-ONLY SYSTEM DETECTED") >> temp_gpu_detect.py
echo     print("=" * 40) >> temp_gpu_detect.py
echo     print("No CUDA-capable GPU found") >> temp_gpu_detect.py
echo     print() >> temp_gpu_detect.py
echo     print("?? TRANSCRIPTION PERFORMANCE:") >> temp_gpu_detect.py
echo     print("  Expected Speed: Standard CPU processing") >> temp_gpu_detect.py
echo     print("  Recommended Models: medium, small, base") >> temp_gpu_detect.py
echo     print("  Note: Larger models will be slower") >> temp_gpu_detect.py
echo     print() >> temp_gpu_detect.py
echo     print("?? TO ENABLE GPU ACCELERATION:") >> temp_gpu_detect.py
echo     print("  1. Install NVIDIA GPU (GTX 1060+ or RTX series)") >> temp_gpu_detect.py
echo     print("  2. Install NVIDIA drivers") >> temp_gpu_detect.py
echo     print("  3. Install CUDA toolkit") >> temp_gpu_detect.py
echo     print("  4. Install CUDA PyTorch:") >> temp_gpu_detect.py
echo     print("     pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118") >> temp_gpu_detect.py
echo     print() >> temp_gpu_detect.py
echo     print("??  Your system will work fine with CPU transcription!") >> temp_gpu_detect.py

:: Run the detection script
if exist "venv\" (
    echo ?? Using virtual environment...
    call venv\Scripts\activate.bat
    python temp_gpu_detect.py
) else (
    echo ??  No virtual environment found - using system Python
    python temp_gpu_detect.py
)

:: Clean up
del temp_gpu_detect.py

echo.
echo ========================================
echo ?? RECOMMENDATIONS FOR YOUR SYSTEM
echo ========================================
echo.

:: Create recommendation script
echo import torch > temp_recommendations.py
echo. >> temp_recommendations.py
echo if torch.cuda.is_available(): >> temp_recommendations.py
echo     gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3) >> temp_recommendations.py
echo     print("?? OPTIMAL SETTINGS FOR YOUR GPU:") >> temp_recommendations.py
echo     print() >> temp_recommendations.py
echo     if gpu_memory ^>= 8: >> temp_recommendations.py
echo         print("  ? Whisper Model: large-v3 (best quality)") >> temp_recommendations.py
echo         print("  ? Device: CUDA") >> temp_recommendations.py
echo         print("  ? Compute Type: float16") >> temp_recommendations.py
echo         print("  ? Expected Speed: Very Fast") >> temp_recommendations.py
echo     elif gpu_memory ^>= 4: >> temp_recommendations.py
echo         print("  ? Whisper Model: large-v2 or medium") >> temp_recommendations.py
echo         print("  ? Device: CUDA") >> temp_recommendations.py
echo         print("  ? Compute Type: float16") >> temp_recommendations.py
echo         print("  ? Expected Speed: Fast") >> temp_recommendations.py
echo     elif gpu_memory ^>= 2: >> temp_recommendations.py
echo         print("  ??  Whisper Model: medium or small") >> temp_recommendations.py
echo         print("  ? Device: CUDA") >> temp_recommendations.py
echo         print("  ? Compute Type: float16") >> temp_recommendations.py
echo         print("  ? Expected Speed: Moderate") >> temp_recommendations.py
echo     else: >> temp_recommendations.py
echo         print("  ??  Whisper Model: small or base") >> temp_recommendations.py
echo         print("  ? Device: CUDA") >> temp_recommendations.py
echo         print("  ? Compute Type: float16") >> temp_recommendations.py
echo         print("  ??  Expected Speed: Limited by GPU memory") >> temp_recommendations.py
echo else: >> temp_recommendations.py
echo     print("?? OPTIMAL SETTINGS FOR YOUR CPU:") >> temp_recommendations.py
echo     print() >> temp_recommendations.py
echo     print("  ? Whisper Model: medium or small") >> temp_recommendations.py
echo     print("  ? Device: CPU") >> temp_recommendations.py
echo     print("  ? Compute Type: int8") >> temp_recommendations.py
echo     print("  ??  Expected Speed: Slower but functional") >> temp_recommendations.py
echo     print() >> temp_recommendations.py
echo     print("?? TIP: Start with 'medium' model for good balance") >> temp_recommendations.py
echo     print("    of quality and speed on CPU systems.") >> temp_recommendations.py

:: Run recommendations
if exist "venv\" (
    python temp_recommendations.py
) else (
    python temp_recommendations.py
)

:: Clean up
del temp_recommendations.py

echo.
echo ========================================
echo ?? READY TO START AI MEETING NOTES
echo ========================================
echo.

set /p START_APP="Would you like to start the AI Meeting Notes app now? (y/n): "
if /i "!START_APP!"=="y" (
    echo.
    echo ?? Starting AI Meeting Notes...
    echo.
    call run.bat
) else (
    echo.
    echo ?? To start the application later, run: run.bat
    echo ?? To check GPU status again, run: detect_gpu.bat
    echo.
)

echo Press any key to exit...
pause >nul
