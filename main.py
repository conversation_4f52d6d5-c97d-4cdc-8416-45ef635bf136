import customtkinter as ctk
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
import threading
import datetime
import json
import os
from pathlib import Path
import wave
import numpy as np
import sounddevice as sd
from pydub import AudioSegment
from audio_recorder import AudioRecorder
from transcription_service import TranscriptionService
from ai_assistant import AIAssistant
from export_service import ExportService

# Set appearance mode and color theme
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class MeetingNotesApp:
    def __init__(self):
        # Initialize main window
        self.root = ctk.CTk()
        self.root.title("AI Meeting Notes - Voice Recorder & Transcription")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Initialize services
        self.audio_recorder = AudioRecorder()
        self.transcription_service = TranscriptionService()
        self.ai_assistant = AIAssistant()
        self.export_service = ExportService()
        
        # Initialize variables
        self.current_transcription = ""
        self.current_meeting_data = {}
        self.meetings_data = self.load_meetings_data()
        
        # Create GUI
        self.create_widgets()
        self.setup_layout()
        
    def create_widgets(self):
        # Main container
        self.main_frame = ctk.CTkFrame(self.root)
        
        # Header
        self.header_frame = ctk.CTkFrame(self.main_frame)
        self.title_label = ctk.CTkLabel(
            self.header_frame, 
            text="🎙️ AI Meeting Notes", 
            font=ctk.CTkFont(size=24, weight="bold")
        )
        
        # Left panel - Recording and Controls
        self.left_panel = ctk.CTkFrame(self.main_frame)
        
        # Recording section
        self.recording_frame = ctk.CTkFrame(self.left_panel)
        self.recording_label = ctk.CTkLabel(
            self.recording_frame, 
            text="Audio Recording", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        
        self.record_button = ctk.CTkButton(
            self.recording_frame,
            text="🔴 Start Recording",
            command=self.toggle_recording,
            height=40,
            font=ctk.CTkFont(size=14)
        )
        
        self.recording_status = ctk.CTkLabel(
            self.recording_frame,
            text="Ready to record",
            font=ctk.CTkFont(size=12)
        )
        
        self.recording_time = ctk.CTkLabel(
            self.recording_frame,
            text="00:00:00",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        
        # File upload section
        self.upload_frame = ctk.CTkFrame(self.left_panel)
        self.upload_label = ctk.CTkLabel(
            self.upload_frame, 
            text="Upload Audio File", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        
        self.upload_button = ctk.CTkButton(
            self.upload_frame,
            text="📁 Upload Audio File",
            command=self.upload_audio_file,
            height=40
        )
        
        # Meeting info section
        self.info_frame = ctk.CTkFrame(self.left_panel)
        self.info_label = ctk.CTkLabel(
            self.info_frame, 
            text="Meeting Information", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        
        self.meeting_title_entry = ctk.CTkEntry(
            self.info_frame,
            placeholder_text="Meeting Title",
            height=35
        )
        
        self.meeting_date_entry = ctk.CTkEntry(
            self.info_frame,
            placeholder_text="Date (YYYY-MM-DD)",
            height=35
        )
        self.meeting_date_entry.insert(0, datetime.datetime.now().strftime("%Y-%m-%d"))
        
        self.participants_entry = ctk.CTkEntry(
            self.info_frame,
            placeholder_text="Participants (comma separated)",
            height=35
        )
        
        # Transcription controls
        self.transcribe_frame = ctk.CTkFrame(self.left_panel)
        self.transcribe_label = ctk.CTkLabel(
            self.transcribe_frame, 
            text="Transcription", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        
        # Whisper model selection
        self.whisper_model_frame = ctk.CTkFrame(self.transcribe_frame)
        self.whisper_model_label = ctk.CTkLabel(
            self.whisper_model_frame,
            text="Whisper Model:",
            font=ctk.CTkFont(size=12)
        )
        
        # Get available Whisper models
        available_whisper_models = self.transcription_service.get_available_models()
        self.whisper_model_dropdown = ctk.CTkComboBox(
            self.whisper_model_frame,
            values=available_whisper_models,
            command=self.on_whisper_model_change,
            width=150
        )
        self.whisper_model_dropdown.set(self.transcription_service.model_size)

        # GPU info button
        self.gpu_info_button = ctk.CTkButton(
            self.whisper_model_frame,
            text="🔍",
            command=self.show_gpu_info,
            width=30,
            height=28,
            fg_color="blue",
            hover_color="darkblue"
        )

        # Whisper optimization info
        device_info = self.transcription_service.get_device_info()
        info_text = self._format_device_info(device_info)

        self.whisper_info_label = ctk.CTkLabel(
            self.whisper_model_frame,
            text=info_text,
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        
        self.transcribe_button = ctk.CTkButton(
            self.transcribe_frame,
            text="🔤 Transcribe Audio",
            command=self.transcribe_audio,
            height=40
        )
        
        self.transcription_progress = ctk.CTkProgressBar(self.transcribe_frame)
        self.transcription_progress.set(0)
        
        # Right panel - Transcription and AI features
        self.right_panel = ctk.CTkFrame(self.main_frame)
        
        # Transcription display
        self.transcription_frame = ctk.CTkFrame(self.right_panel)
        self.transcription_label = ctk.CTkLabel(
            self.transcription_frame, 
            text="Transcription", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        
        self.transcription_text = ctk.CTkTextbox(
            self.transcription_frame,
            height=300,
            font=ctk.CTkFont(size=12)
        )
        
        # AI features
        self.ai_frame = ctk.CTkFrame(self.right_panel)
        self.ai_label = ctk.CTkLabel(
            self.ai_frame, 
            text="AI Assistant", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        
        # LLM Provider selection
        self.provider_frame = ctk.CTkFrame(self.ai_frame)
        self.provider_label = ctk.CTkLabel(
            self.provider_frame,
            text="LLM Provider:",
            font=ctk.CTkFont(size=12)
        )
        
        self.provider_dropdown = ctk.CTkComboBox(
            self.provider_frame,
            values=["openai", "gemini", "groq", "anthropic", "ollama", "lmstudio", "cohere"],
            command=self.on_provider_change,
            width=120
        )
        self.provider_dropdown.set(self.ai_assistant.provider)
        
        self.model_label = ctk.CTkLabel(
            self.provider_frame,
            text="Model:",
            font=ctk.CTkFont(size=12)
        )
        
        self.model_dropdown = ctk.CTkComboBox(
            self.provider_frame,
            values=[self.ai_assistant.model],
            command=self.on_model_change,
            width=150
        )
        self.model_dropdown.set(self.ai_assistant.model)
        
        self.refresh_models_button = ctk.CTkButton(
            self.provider_frame,
            text="🔄",
            command=self.refresh_available_models,
            width=30,
            height=28
        )

        # Manual model entry for local LLMs
        self.manual_model_entry = ctk.CTkEntry(
            self.provider_frame,
            placeholder_text="Enter model name...",
            width=150,
            height=28
        )

        self.set_manual_model_button = ctk.CTkButton(
            self.provider_frame,
            text="Set",
            command=self.set_manual_model,
            width=40,
            height=28
        )

        # Initially hide manual entry (show only for local LLMs)
        self.update_manual_model_visibility()

        # Reload config button
        self.reload_config_button = ctk.CTkButton(
            self.provider_frame,
            text="⚙️",
            command=self.reload_env_config,
            width=30,
            height=28,
            fg_color="orange",
            hover_color="darkorange"
        )
        
        self.question_entry = ctk.CTkEntry(
            self.ai_frame,
            placeholder_text="Ask a question about the meeting...",
            height=35
        )
        
        self.ask_button = ctk.CTkButton(
            self.ai_frame,
            text="❓ Ask Question",
            command=self.ask_question,
            height=35
        )
        
        self.summary_button = ctk.CTkButton(
            self.ai_frame,
            text="📝 Generate Summary",
            command=self.generate_summary,
            height=35
        )
        
        self.ai_response_text = ctk.CTkTextbox(
            self.ai_frame,
            height=200,
            font=ctk.CTkFont(size=12)
        )
        
        # Export section
        self.export_frame = ctk.CTkFrame(self.right_panel)
        self.export_label = ctk.CTkLabel(
            self.export_frame, 
            text="Export", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        
        self.export_pdf_button = ctk.CTkButton(
            self.export_frame,
            text="📄 Export as PDF",
            command=lambda: self.export_meeting("pdf"),
            height=35
        )
        
        self.export_word_button = ctk.CTkButton(
            self.export_frame,
            text="📝 Export as Word",
            command=lambda: self.export_meeting("docx"),
            height=35
        )
        
        self.export_txt_button = ctk.CTkButton(
            self.export_frame,
            text="📄 Export as Text",
            command=lambda: self.export_meeting("txt"),
            height=35
        )
        
        # Previous meetings
        self.history_frame = ctk.CTkFrame(self.left_panel)
        self.history_header_frame = ctk.CTkFrame(self.history_frame)
        self.history_label = ctk.CTkLabel(
            self.history_header_frame,
            text="Previous Meetings",
            font=ctk.CTkFont(size=16, weight="bold")
        )

        self.clear_all_button = ctk.CTkButton(
            self.history_header_frame,
            text="🗑️ Clear All",
            command=self.clear_all_meetings,
            width=80,
            height=28,
            fg_color="red",
            hover_color="darkred"
        )

        self.meetings_listbox = ctk.CTkScrollableFrame(self.history_frame, height=150)
        self.refresh_meetings_list()
        
    def setup_layout(self):
        # Main layout
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Header
        self.header_frame.pack(fill="x", pady=(0, 10))
        self.title_label.pack(pady=10)
        
        # Create two-column layout
        self.left_panel.pack(side="left", fill="both", expand=True, padx=(0, 5))
        self.right_panel.pack(side="right", fill="both", expand=True, padx=(5, 0))
        
        # Left panel layout
        self.recording_frame.pack(fill="x", pady=(0, 10))
        self.recording_label.pack(pady=(10, 5))
        self.record_button.pack(pady=5)
        self.recording_status.pack(pady=2)
        self.recording_time.pack(pady=2)
        
        self.upload_frame.pack(fill="x", pady=(0, 10))
        self.upload_label.pack(pady=(10, 5))
        self.upload_button.pack(pady=5)
        
        self.info_frame.pack(fill="x", pady=(0, 10))
        self.info_label.pack(pady=(10, 5))
        self.meeting_title_entry.pack(pady=3, padx=10, fill="x")
        self.meeting_date_entry.pack(pady=3, padx=10, fill="x")
        self.participants_entry.pack(pady=3, padx=10, fill="x")
        
        self.transcribe_frame.pack(fill="x", pady=(0, 10))
        self.transcribe_label.pack(pady=(10, 5))
        
        # Whisper model selection layout
        self.whisper_model_frame.pack(fill="x", padx=10, pady=5)
        self.whisper_model_label.pack(side="left", padx=(10, 5))
        self.whisper_model_dropdown.pack(side="left", padx=5)
        self.gpu_info_button.pack(side="left", padx=5)
        self.whisper_info_label.pack(side="left", padx=(10, 5))
        
        self.transcribe_button.pack(pady=5)
        self.transcription_progress.pack(pady=5, padx=10, fill="x")
        
        self.history_frame.pack(fill="both", expand=True)
        self.history_header_frame.pack(fill="x", padx=10, pady=(10, 5))
        self.history_label.pack(side="left")
        self.clear_all_button.pack(side="right")
        self.meetings_listbox.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # Right panel layout
        self.transcription_frame.pack(fill="both", expand=True, pady=(0, 10))
        self.transcription_label.pack(pady=(10, 5))
        self.transcription_text.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        self.ai_frame.pack(fill="x", pady=(0, 10))
        self.ai_label.pack(pady=(10, 5))
        
        # Provider selection layout
        self.provider_frame.pack(fill="x", padx=10, pady=5)
        self.provider_label.pack(side="left", padx=(10, 5))
        self.provider_dropdown.pack(side="left", padx=5)
        self.model_label.pack(side="left", padx=(10, 5))
        self.model_dropdown.pack(side="left", padx=5)
        self.refresh_models_button.pack(side="left", padx=5)
        self.reload_config_button.pack(side="left", padx=5)
        # Manual model entry will be packed dynamically based on provider
        
        # AI controls in horizontal layout
        ai_controls_frame = ctk.CTkFrame(self.ai_frame)
        ai_controls_frame.pack(fill="x", padx=10, pady=5)
        
        # Pack question controls into the frame
        self.question_entry.pack(in_=ai_controls_frame, side="left", fill="x", expand=True, padx=(10, 5))
        self.ask_button.pack(in_=ai_controls_frame, side="right", padx=(5, 10))
        
        self.summary_button.pack(pady=5)
        self.ai_response_text.pack(fill="x", padx=10, pady=(0, 10))
        
        self.export_frame.pack(fill="x")
        self.export_label.pack(pady=(10, 5))
        
        # Export buttons in horizontal layout
        export_buttons_frame = ctk.CTkFrame(self.export_frame)
        export_buttons_frame.pack(fill="x", padx=10, pady=(0, 10))
        self.export_pdf_button.pack(side="left", expand=True, padx=2)
        self.export_word_button.pack(side="left", expand=True, padx=2)
        self.export_txt_button.pack(side="left", expand=True, padx=2)
        
    def toggle_recording(self):
        if not self.audio_recorder.is_recording:
            self.start_recording()
        else:
            self.stop_recording()
    
    def start_recording(self):
        try:
            self.audio_recorder.start_recording()
            self.record_button.configure(text="⏹️ Stop Recording")
            self.recording_status.configure(text="Recording...")
            self.update_recording_time()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start recording: {str(e)}")
    
    def stop_recording(self):
        try:
            filename = self.audio_recorder.stop_recording()
            self.record_button.configure(text="🔴 Start Recording")
            self.recording_status.configure(text=f"Saved: {filename}")
            self.recording_time.configure(text="00:00:00")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop recording: {str(e)}")
    
    def update_recording_time(self):
        if self.audio_recorder.is_recording:
            duration = self.audio_recorder.get_recording_duration()
            time_str = str(datetime.timedelta(seconds=int(duration)))
            self.recording_time.configure(text=time_str)
            self.root.after(1000, self.update_recording_time)
    
    def upload_audio_file(self):
        filetypes = [
            ("Audio files", "*.wav *.mp3 *.m4a *.flac *.ogg"),
            ("All files", "*.*")
        ]
        filename = filedialog.askopenfilename(
            title="Select audio file",
            filetypes=filetypes
        )
        if filename:
            self.transcription_service.audio_file = filename
            self.recording_status.configure(text=f"Loaded: {os.path.basename(filename)}")
    
    def transcribe_audio(self):
        if not self.transcription_service.audio_file:
            messagebox.showwarning("Warning", "Please record or upload an audio file first.")
            return
        
        def transcribe_thread():
            try:
                self.transcription_progress.set(0.1)
                self.transcribe_button.configure(text="Transcribing...", state="disabled")
                
                # Update progress
                self.transcription_progress.set(0.5)
                
                # Perform transcription
                transcription = self.transcription_service.transcribe()
                
                self.transcription_progress.set(1.0)
                self.current_transcription = transcription
                
                # Update UI on main thread
                self.root.after(0, lambda: self.update_transcription_ui(transcription))
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("Error", f"Transcription failed: {str(e)}"))
            finally:
                self.root.after(0, lambda: self.transcribe_button.configure(
                    text="🔤 Transcribe Audio", state="normal"
                ))
                self.root.after(0, lambda: self.transcription_progress.set(0))
        
        threading.Thread(target=transcribe_thread, daemon=True).start()
    
    def update_transcription_ui(self, transcription):
        self.transcription_text.delete("1.0", "end")
        self.transcription_text.insert("1.0", transcription)
    
    def ask_question(self):
        question = self.question_entry.get().strip()
        if not question:
            messagebox.showwarning("Warning", "Please enter a question.")
            return
        
        if not self.current_transcription:
            messagebox.showwarning("Warning", "Please transcribe audio first.")
            return
        
        def ask_thread():
            try:
                self.ask_button.configure(text="Thinking...", state="disabled")
                response = self.ai_assistant.ask_question(self.current_transcription, question)
                self.root.after(0, lambda: self.update_ai_response(response))
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("Error", f"AI request failed: {str(e)}"))
            finally:
                self.root.after(0, lambda: self.ask_button.configure(
                    text="❓ Ask Question", state="normal"
                ))
        
        threading.Thread(target=ask_thread, daemon=True).start()
    
    def generate_summary(self):
        if not self.current_transcription:
            messagebox.showwarning("Warning", "Please transcribe audio first.")
            return
        
        def summary_thread():
            try:
                self.summary_button.configure(text="Generating...", state="disabled")
                summary = self.ai_assistant.generate_summary(self.current_transcription)
                self.root.after(0, lambda: self.update_ai_response(summary))
                
                # Save meeting data
                self.save_meeting_data(summary)
                
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("Error", f"Summary generation failed: {str(e)}"))
            finally:
                self.root.after(0, lambda: self.summary_button.configure(
                    text="📝 Generate Summary", state="normal"
                ))
        
        threading.Thread(target=summary_thread, daemon=True).start()
    
    def update_ai_response(self, response):
        self.ai_response_text.delete("1.0", "end")
        self.ai_response_text.insert("1.0", response)
    
    def save_meeting_data(self, summary):
        meeting_data = {
            "title": self.meeting_title_entry.get() or "Untitled Meeting",
            "date": self.meeting_date_entry.get(),
            "participants": self.participants_entry.get(),
            "transcription": self.current_transcription,
            "summary": summary,
            "timestamp": datetime.datetime.now().isoformat()
        }
        
        self.current_meeting_data = meeting_data
        meeting_id = f"meeting_{len(self.meetings_data) + 1}"
        self.meetings_data[meeting_id] = meeting_data
        
        # Save to file
        os.makedirs("meetings", exist_ok=True)
        with open("meetings/meetings_data.json", "w", encoding="utf-8") as f:
            json.dump(self.meetings_data, f, indent=2, ensure_ascii=False)
        
        self.refresh_meetings_list()
    
    def load_meetings_data(self):
        try:
            with open("meetings/meetings_data.json", "r", encoding="utf-8") as f:
                return json.load(f)
        except FileNotFoundError:
            return {}
    
    def refresh_meetings_list(self):
        # Clear existing buttons
        for widget in self.meetings_listbox.winfo_children():
            widget.destroy()

        # Add meeting buttons with delete functionality
        for meeting_id, meeting_data in self.meetings_data.items():
            meeting_frame = ctk.CTkFrame(self.meetings_listbox)
            meeting_frame.pack(fill="x", padx=5, pady=2)

            meeting_button = ctk.CTkButton(
                meeting_frame,
                text=f"{meeting_data['title']} - {meeting_data['date']}",
                command=lambda mid=meeting_id: self.load_meeting(mid),
                height=30
            )
            meeting_button.pack(side="left", fill="x", expand=True, padx=(5, 2))

            delete_button = ctk.CTkButton(
                meeting_frame,
                text="🗑️",
                command=lambda mid=meeting_id: self.delete_meeting(mid),
                width=30,
                height=30,
                fg_color="red",
                hover_color="darkred"
            )
            delete_button.pack(side="right", padx=(2, 5))
    
    def load_meeting(self, meeting_id):
        meeting_data = self.meetings_data[meeting_id]
        self.current_meeting_data = meeting_data
        self.current_transcription = meeting_data["transcription"]
        
        # Update UI
        self.meeting_title_entry.delete(0, "end")
        self.meeting_title_entry.insert(0, meeting_data["title"])
        
        self.meeting_date_entry.delete(0, "end")
        self.meeting_date_entry.insert(0, meeting_data["date"])
        
        self.participants_entry.delete(0, "end")
        self.participants_entry.insert(0, meeting_data["participants"])
        
        self.transcription_text.delete("1.0", "end")
        self.transcription_text.insert("1.0", meeting_data["transcription"])
        
        self.ai_response_text.delete("1.0", "end")
        self.ai_response_text.insert("1.0", meeting_data["summary"])
    
    def export_meeting(self, format_type):
        if not self.current_meeting_data and not self.current_transcription:
            messagebox.showwarning("Warning", "No meeting data to export.")
            return
        
        try:
            filename = self.export_service.export_meeting(
                self.current_meeting_data or {
                    "title": self.meeting_title_entry.get() or "Untitled Meeting",
                    "date": self.meeting_date_entry.get(),
                    "participants": self.participants_entry.get(),
                    "transcription": self.current_transcription,
                    "summary": self.ai_response_text.get("1.0", "end").strip()
                },
                format_type
            )
            messagebox.showinfo("Success", f"Meeting exported as {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Export failed: {str(e)}")
    
    def on_provider_change(self, provider):
        """Handle LLM provider change"""
        try:
            self.ai_assistant.set_provider(provider)
            self.refresh_available_models()
            self.update_manual_model_visibility()

            # Show connection status
            status_text = f"✅ Switched to {provider.upper()} provider\n\n"
            if provider in ['ollama', 'lmstudio']:
                if self.ai_assistant.api_available:
                    status_text += "✅ Connected - Auto-detected models loaded"
                else:
                    status_text += "❌ Service not running - Use manual model entry below"
            else:
                status_text += f"Status: {'✅ Connected' if self.ai_assistant.api_available else '❌ Not configured'}"

            self.ai_response_text.delete("1.0", "end")
            self.ai_response_text.insert("1.0", status_text)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to switch provider: {str(e)}")
    
    def on_model_change(self, model):
        """Handle model change"""
        try:
            self.ai_assistant.set_model(model)
            self.ai_response_text.delete("1.0", "end")
            self.ai_response_text.insert("1.0", f"✅ Switched to model: {model}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to switch model: {str(e)}")
    
    def refresh_available_models(self):
        """Refresh the list of available models for the current provider"""
        try:
            models = self.ai_assistant.get_available_models()
            self.model_dropdown.configure(values=models)
            if models and self.ai_assistant.model in models:
                self.model_dropdown.set(self.ai_assistant.model)
            elif models:
                self.model_dropdown.set(models[0])
                self.ai_assistant.set_model(models[0])
        except Exception as e:
            print(f"Error refreshing models: {e}")

    def on_whisper_model_change(self, model):
        """Handle Whisper model change"""
        try:
            self.transcription_service.set_model(model)
            device_info = self.transcription_service.get_device_info()
            info_text = self._format_device_info(device_info)
            self.whisper_info_label.configure(text=info_text)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to switch Whisper model: {str(e)}")

    def _format_device_info(self, device_info):
        """Format device information for display"""
        if device_info['device'] == 'cuda' and device_info['cuda_available']:
            gpu_name = device_info['gpu_name'] or "Unknown GPU"
            gpu_memory = device_info['gpu_memory']
            return f"🚀 GPU: {gpu_name} ({gpu_memory:.1f}GB) | {device_info['compute_type']}"
        else:
            return f"🖥️ CPU | {device_info['compute_type']}"

    def show_gpu_info(self):
        """Show detailed GPU and CUDA information"""
        try:
            cuda_info = self.transcription_service.get_cuda_info()
            device_info = self.transcription_service.get_device_info()

            info_text = "🔍 GPU & CUDA Information\n"
            info_text += "=" * 40 + "\n\n"

            info_text += f"PyTorch Version: {cuda_info['torch_version']}\n"
            info_text += f"CUDA Available: {'✅ Yes' if cuda_info['cuda_available'] else '❌ No'}\n"

            if cuda_info['cuda_available']:
                info_text += f"CUDA Version: {cuda_info['cuda_version']}\n"
                info_text += f"cuDNN Version: {cuda_info['cudnn_version']}\n"
                info_text += f"GPU Count: {cuda_info['gpu_count']}\n\n"

                for i, gpu in enumerate(cuda_info['gpus']):
                    info_text += f"GPU {i}: {gpu['name']}\n"
                    info_text += f"  Memory: {gpu['memory']:.1f} GB\n"
                    info_text += f"  Compute Capability: {gpu['compute_capability']}\n\n"

                info_text += f"Current Device: {device_info['device']}\n"
                info_text += f"Compute Type: {device_info['compute_type']}\n"
            else:
                info_text += "\n💡 To enable GPU acceleration:\n"
                info_text += "1. Install NVIDIA GPU with CUDA support\n"
                info_text += "2. Install NVIDIA drivers\n"
                info_text += "3. Install CUDA toolkit\n"
                info_text += "4. Install CUDA-compatible PyTorch:\n"
                info_text += "   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118\n"

            # Show in a popup window
            popup = ctk.CTkToplevel(self.root)
            popup.title("GPU & CUDA Information")
            popup.geometry("600x500")
            popup.transient(self.root)
            popup.grab_set()

            text_widget = ctk.CTkTextbox(popup, font=ctk.CTkFont(family="Consolas", size=12))
            text_widget.pack(fill="both", expand=True, padx=20, pady=20)
            text_widget.insert("1.0", info_text)
            text_widget.configure(state="disabled")

            close_button = ctk.CTkButton(popup, text="Close", command=popup.destroy)
            close_button.pack(pady=(0, 20))

        except Exception as e:
            messagebox.showerror("Error", f"Failed to get GPU information: {str(e)}")

    def delete_meeting(self, meeting_id):
        """Delete a specific meeting with confirmation"""
        meeting_data = self.meetings_data.get(meeting_id)
        if not meeting_data:
            return

        # Confirmation dialog
        result = messagebox.askyesno(
            "Delete Meeting",
            f"Are you sure you want to delete the meeting:\n'{meeting_data['title']}' - {meeting_data['date']}?\n\nThis action cannot be undone."
        )

        if result:
            try:
                # Remove from data
                del self.meetings_data[meeting_id]

                # Save updated data
                os.makedirs("meetings", exist_ok=True)
                with open("meetings/meetings_data.json", "w", encoding="utf-8") as f:
                    json.dump(self.meetings_data, f, indent=2, ensure_ascii=False)

                # Refresh the list
                self.refresh_meetings_list()

                messagebox.showinfo("Success", "Meeting deleted successfully.")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to delete meeting: {str(e)}")

    def clear_all_meetings(self):
        """Clear all meetings with confirmation"""
        if not self.meetings_data:
            messagebox.showinfo("Info", "No meetings to clear.")
            return

        # Confirmation dialog
        result = messagebox.askyesno(
            "Clear All Meetings",
            f"Are you sure you want to delete ALL {len(self.meetings_data)} meetings?\n\nThis action cannot be undone."
        )

        if result:
            try:
                # Clear all data
                self.meetings_data = {}

                # Save empty data
                os.makedirs("meetings", exist_ok=True)
                with open("meetings/meetings_data.json", "w", encoding="utf-8") as f:
                    json.dump(self.meetings_data, f, indent=2, ensure_ascii=False)

                # Refresh the list
                self.refresh_meetings_list()

                messagebox.showinfo("Success", "All meetings cleared successfully.")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to clear meetings: {str(e)}")

    def update_manual_model_visibility(self):
        """Show/hide manual model entry based on provider"""
        provider = self.ai_assistant.provider

        if provider in ['ollama', 'lmstudio']:
            # Show manual entry for local LLMs
            self.manual_model_entry.pack(side="left", padx=(10, 5))
            self.set_manual_model_button.pack(side="left", padx=5)

            # Update placeholder text
            if provider == 'ollama':
                self.manual_model_entry.configure(placeholder_text="e.g., llama3.2, mistral...")
            else:  # lmstudio
                self.manual_model_entry.configure(placeholder_text="e.g., local-model...")
        else:
            # Hide manual entry for cloud providers
            self.manual_model_entry.pack_forget()
            self.set_manual_model_button.pack_forget()

    def set_manual_model(self):
        """Set a manually entered model name"""
        model_name = self.manual_model_entry.get().strip()
        if not model_name:
            messagebox.showwarning("Warning", "Please enter a model name.")
            return

        try:
            # Add the model to the dropdown if not already there
            current_values = list(self.model_dropdown.cget("values"))
            if model_name not in current_values:
                current_values.append(model_name)
                self.model_dropdown.configure(values=current_values)

            # Set the model
            self.model_dropdown.set(model_name)
            self.ai_assistant.set_model(model_name)

            # Clear the entry
            self.manual_model_entry.delete(0, "end")

            # Update status
            self.ai_response_text.delete("1.0", "end")
            self.ai_response_text.insert("1.0", f"✅ Manual model set: {model_name}\n\nNote: Make sure this model is available in your {self.ai_assistant.provider.upper()} installation.")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to set manual model: {str(e)}")

    def reload_env_config(self):
        """Reload configuration from .env file"""
        try:
            # Reload AI assistant configuration
            self.ai_assistant.reload_env_config()

            # Reload transcription service configuration
            self.transcription_service.__init__()

            # Update GUI elements
            self.provider_dropdown.set(self.ai_assistant.provider)
            self.refresh_available_models()
            self.update_manual_model_visibility()

            # Update whisper model dropdown
            available_whisper_models = self.transcription_service.get_available_models()
            self.whisper_model_dropdown.configure(values=available_whisper_models)
            self.whisper_model_dropdown.set(self.transcription_service.model_size)

            # Update device info display
            device_info = self.transcription_service.get_device_info()
            info_text = self._format_device_info(device_info)
            self.whisper_info_label.configure(text=info_text)

            # Show success message
            self.ai_response_text.delete("1.0", "end")
            self.ai_response_text.insert("1.0", "✅ Configuration reloaded from .env file\n\nAll settings have been updated from the .env file.")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to reload configuration: {str(e)}")
    
    def run(self):
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
    
    def on_closing(self):
        if self.audio_recorder.is_recording:
            self.audio_recorder.stop_recording()
        self.root.destroy()

if __name__ == "__main__":
    app = MeetingNotesApp()
    app.run()
