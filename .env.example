# ==============================================
# AI MEETING NOTES - CONFIGURATION
# ==============================================

# ==============================================
# LLM PROVIDER SELECTION
# ==============================================
# Choose your preferred AI provider (uncomment one):
LLM_PROVIDER=openai
# LLM_PROVIDER=gemini
# LLM_PROVIDER=groq
# LLM_PROVIDER=anthropic
# LLM_PROVIDER=ollama
# LLM_PROVIDER=lmstudio
# LLM_PROVIDER=cohere

# ==============================================
# CLOUD API PROVIDERS (FREE/PAID)
# ==============================================

# OpenAI (Latest 2025 Models) - Get key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=
OPENAI_MODEL=gpt-4o-mini
# Latest 2025 models (uncomment to use):
# OPENAI_MODEL=gpt-4.1-2025-04-14
# OPENAI_MODEL=o4-mini-2025-04-16
# OPENAI_MODEL=o3-2025-04-16
# OPENAI_MODEL=gpt-4o
# OPENAI_MODEL=gpt-4-turbo
# Legacy models:
# OPENAI_MODEL=gpt-3.5-turbo
# OPENAI_MODEL=gpt-4

# Google Gemini (FREE) - Get key from: https://aistudio.google.com/app/apikey
GEMINI_API_KEY=
GEMINI_MODEL=gemini-2.5-flash
# Available models (uncomment to use):
# GEMINI_MODEL=gemini-2.5-pro
# GEMINI_MODEL=gemini-2.5-flash-lite-preview-06-17
# GEMINI_MODEL=gemini-1.5-pro
# GEMINI_MODEL=gemini-1.5-flash

# Groq (FREE - Latest 2025 Models) - Get key from: https://console.groq.com/keys
GROQ_API_KEY=
GROQ_MODEL=llama-3.3-70b-versatile
# Latest 2025 models (uncomment to use):
# GROQ_MODEL=llama-3.3-70b-specdec
# GROQ_MODEL=llama-3.2-90b-vision-preview
# GROQ_MODEL=llama-3.2-90b-text-preview
# GROQ_MODEL=llama-3.2-11b-vision-preview
# GROQ_MODEL=llama-3.2-3b-preview
# GROQ_MODEL=llama-3.2-1b-preview
# GROQ_MODEL=gemma2-9b-it
# GROQ_MODEL=gemma-7b-it
# Legacy models:
# GROQ_MODEL=llama3-8b-8192
# GROQ_MODEL=llama3-70b-8192
# GROQ_MODEL=mixtral-8x7b-32768

# Anthropic Claude (Latest 2025 Models) - Get key from: https://console.anthropic.com/
ANTHROPIC_API_KEY=
ANTHROPIC_MODEL=claude-3-5-haiku-20241022
# Latest 2025 models (uncomment to use):
# ANTHROPIC_MODEL=claude-3-5-sonnet-20241022
# ANTHROPIC_MODEL=claude-3-5-opus-20241022
# Legacy models:
# ANTHROPIC_MODEL=claude-3-haiku-20240307
# ANTHROPIC_MODEL=claude-3-sonnet-20240229
# ANTHROPIC_MODEL=claude-3-opus-20240229

# Cohere (FREE tier available) - Get key from: https://dashboard.cohere.ai/api-keys
COHERE_API_KEY=
COHERE_MODEL=command-light
# COHERE_MODEL=command
# COHERE_MODEL=command-nightly

# ==============================================
# LOCAL LLM PROVIDERS (FREE)
# ==============================================

# Ollama (Local) - Download from: https://ollama.ai/
# Start Ollama and run: ollama pull llama3.2
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2
# OLLAMA_MODEL=llama3.1
# OLLAMA_MODEL=mistral
# OLLAMA_MODEL=codellama

# LM Studio (Local) - Download from: https://lmstudio.ai/
# Start LM Studio server (default port 1234)
LMSTUDIO_URL=http://localhost:1234
LMSTUDIO_MODEL=local-model

# ==============================================
# UNIFIED MODEL SETTING (Optional)
# ==============================================
# You can override the model for any provider here:
# LLM_MODEL=your_preferred_model

# ==============================================
# FLEXIBLE MODEL LISTS (For GUI Dropdowns)
# ==============================================
# These lists will populate the model dropdowns in the GUI
# Add your custom models here separated by commas

# OpenAI Models (add custom models here)
OPENAI_MODELS=gpt-4.1-2025-04-14,o4-mini-2025-04-16,o3-2025-04-16,gpt-4o,gpt-4o-mini,gpt-4-turbo,gpt-3.5-turbo,gpt-4

# Gemini Models
GEMINI_MODELS=gemini-2.5-pro,gemini-2.5-flash,gemini-2.5-flash-lite-preview-06-17,gemini-1.5-pro,gemini-1.5-flash

# Groq Models
GROQ_MODELS=llama-3.3-70b-versatile,llama-3.3-70b-specdec,llama-3.2-90b-vision-preview,llama-3.2-90b-text-preview,llama-3.2-11b-vision-preview,llama-3.2-3b-preview,llama-3.2-1b-preview,gemma2-9b-it,gemma-7b-it,llama3-8b-8192,llama3-70b-8192,mixtral-8x7b-32768

# Anthropic Models
ANTHROPIC_MODELS=claude-3-5-haiku-20241022,claude-3-5-sonnet-20241022,claude-3-5-opus-20241022,claude-3-haiku-20240307,claude-3-sonnet-20240229,claude-3-opus-20240229

# Cohere Models
COHERE_MODELS=command-light,command,command-nightly,command-r,command-r-plus

# Ollama Models (add your downloaded models here)
OLLAMA_MODELS=llama3.2,llama3.1,llama3,mistral,codellama,phi3,gemma2,qwen2.5,deepseek-coder

# LM Studio Models (add your loaded models here)
LMSTUDIO_MODELS=local-model,llama-3.2-3b,mistral-7b,codellama-7b,phi-3-mini

# ==============================================
# AUDIO & TRANSCRIPTION SETTINGS
# ==============================================

# Audio Recording Settings
SAMPLE_RATE=44100
CHANNELS=1

# Whisper Model Settings
WHISPER_MODEL=large-v3
# Available Whisper models (add to dropdown)
WHISPER_MODELS=large-v3,large-v2,large-v1,medium,small,base,tiny

# Whisper Device Settings (auto, cpu, cuda)
WHISPER_DEVICE=auto
WHISPER_COMPUTE_TYPE=auto

# ==============================================
# SETUP INSTRUCTIONS
# ==============================================
# 
# 1. Choose your LLM provider by setting LLM_PROVIDER
# 2. Add the corresponding API key
# 3. Optionally customize the model
# 
# FREE OPTIONS:
# - Gemini: 15 requests/minute, generous quotas
# - Groq: Very fast inference, good free tier
# - Ollama: Completely local, no API needed
# - LM Studio: Local with GUI, easy model management
# 
# PAID OPTIONS:
# - OpenAI: Industry standard, high quality
# - Anthropic: Claude models, great for analysis
# - Cohere: Good for enterprise use
#
