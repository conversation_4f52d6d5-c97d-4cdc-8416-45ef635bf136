import math
import subprocess


print('''Contributors
============

All contributors (by number of commits):
''')


email_map = {

    # Maintainers.
    '*****************': '<EMAIL>',
    '<EMAIL>': '<EMAIL>',
    '<EMAIL>': '<EMAIL>',
    '<EMAIL>': '<EMAIL>',
    '<EMAIL>': '<EMAIL>',

    # Junk.
    'mark@mark-VirtualBox.(none)': None,

    # Aliases.
    '<EMAIL>': '<EMAIL>',
    '<EMAIL>': '<EMAIL>',
    '<EMAIL>': '<EMAIL>',
    '<EMAIL>': '<EMAIL>',

}

name_map = {
    '<EMAIL>': '<PERSON>',
    '<EMAIL>': '<PERSON>',
    'mg<PERSON><PERSON><PERSON>@cls.fr': '<PERSON>',
    '<EMAIL>': '<PERSON>',
    '<EMAIL>': 'Moritz Kassner',
    '<EMAIL>': 'Vidar Tonaas Fauske',
    '<EMAIL>': 'Xinran Xu',
}

github_map = {
    '<EMAIL>': 'billyshambrook',
    '<EMAIL>': 'danielballan',
    '<EMAIL>': 'adavoudi',
    '<EMAIL>': 'mikeboers',
    '<EMAIL>': 'jlaine',
    '<EMAIL>': 'litterfeldt',
    '<EMAIL>': 'markreidvfx',
    '<EMAIL>': 'mkassner',
    '<EMAIL>': 'radek-senfeld',
    '<EMAIL>': 'brendanlong',
    '<EMAIL>': 'tacaswell',
    '<EMAIL>': 'rawler',
    '<EMAIL>': 'vidartf',
    '<EMAIL>': 'willpatera',
    '<EMAIL>': 'xxr3376',
}


email_count = {}
for line in subprocess.check_output(['git', 'log', '--format=%aN,%aE']).decode().splitlines():
    name, email = line.strip().rsplit(',', 1)

    email = email_map.get(email, email)
    if not email:
        continue

    names = name_map.setdefault(email, set())
    if isinstance(names, set):
        names.add(name)

    email_count[email] = email_count.get(email, 0) + 1


last = None
block_i = 0
for email, count in sorted(email_count.items(), key=lambda x: (-x[1], x[0])):

    # This is the natural log, because of course it should be. ;)
    order = int(math.log(count))
    if last and last != order:
        block_i += 1
        print()
    last = order

    names = name_map[email]
    if isinstance(names, set):
        name = ', '.join(sorted(names))
    else:
        name = names

    github = github_map.get(email)

    # The '-' vs '*' is so that Sphinx treats them as different lists, and
    # introduces a gap bettween them.
    if github:
        print('%s %s <%s>; `@%s <https://github.com/%s>`_' % ('-*'[block_i % 2], name, email, github, github))
    else:
        print('%s %s <%s>'      % ('-*'[block_i % 2], name, email,       ))
