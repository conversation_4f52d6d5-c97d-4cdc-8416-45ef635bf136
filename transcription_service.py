from faster_whisper import WhisperModel
import os
from pydub import AudioSegment
import tempfile
from dotenv import load_dotenv
import torch

# Load environment variables
load_dotenv()

class TranscriptionService:
    def __init__(self):
        """
        Initialize the transcription service with Faster Whisper
        """
        # Load configuration from .env
        self.model_size = os.getenv('WHISPER_MODEL', 'large-v3')
        self.device = os.getenv('WHISPER_DEVICE', 'auto')
        self.compute_type = os.getenv('WHISPER_COMPUTE_TYPE', 'auto')
        
        self.model = None
        self.audio_file = None
        
        # Determine optimal device and compute type
        self._optimize_settings()
        
        print(f"Transcription service initialized with model: {self.model_size}")
        print(f"Device: {self.device}, Compute type: {self.compute_type}")
    
    def _optimize_settings(self):
        """Optimize device and compute type settings with NVIDIA GPU detection"""
        if self.device == 'auto':
            # Check for NVIDIA GPU with CUDA support
            if torch.cuda.is_available():
                # Get GPU info
                gpu_count = torch.cuda.device_count()
                gpu_name = torch.cuda.get_device_name(0) if gpu_count > 0 else "Unknown"
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3) if gpu_count > 0 else 0

                self.device = 'cuda'
                print(f"🚀 NVIDIA GPU detected: {gpu_name}")
                print(f"💾 GPU Memory: {gpu_memory:.1f} GB")
                print(f"🔥 Using CUDA acceleration for faster transcription")

                # Check if GPU has enough memory for the model
                if gpu_memory < 2.0 and self.model_size in ['large-v3', 'large-v2', 'large-v1']:
                    print(f"⚠️  Warning: GPU has limited memory ({gpu_memory:.1f} GB)")
                    print(f"💡 Consider using 'medium' or 'small' model for better performance")

            else:
                self.device = 'cpu'
                print("🖥️  No CUDA-capable GPU found - using CPU")
                print("💡 For faster transcription, install CUDA-compatible PyTorch")

        if self.compute_type == 'auto':
            if self.device == 'cuda':
                # Use float16 for GPU - best balance of speed and accuracy
                self.compute_type = 'float16'
                print("⚡ Using float16 precision for optimal GPU performance")
            else:
                # Use int8 for CPU to reduce memory usage and improve speed
                self.compute_type = 'int8'
                print("🔧 Using int8 quantization for CPU optimization")
    
    def get_available_models(self) -> list:
        """Get list of available Whisper models from .env"""
        models_str = os.getenv('WHISPER_MODELS', 'large-v3,large-v2,large-v1,medium,small,base,tiny')
        return [model.strip() for model in models_str.split(',') if model.strip()]

    def set_model(self, model_name: str):
        """Change the Whisper model"""
        self.model_size = model_name
        # Clear the current model to force reload with new model
        self.model = None
        print(f"Whisper model changed to: {model_name}")

        # Re-optimize settings for the new model
        self._optimize_settings()
        self._check_model_gpu_compatibility()

    def _check_model_gpu_compatibility(self):
        """Check if current model is compatible with available GPU"""
        if self.device == 'cuda' and torch.cuda.is_available():
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)

            # Model memory requirements (approximate)
            model_memory_requirements = {
                'large-v3': 3.0,
                'large-v2': 3.0,
                'large-v1': 3.0,
                'medium': 1.5,
                'small': 1.0,
                'base': 0.5,
                'tiny': 0.2
            }

            required_memory = model_memory_requirements.get(self.model_size, 2.0)

            if gpu_memory < required_memory:
                print(f"⚠️  Warning: {self.model_size} model may require {required_memory:.1f} GB GPU memory")
                print(f"💾 Available GPU memory: {gpu_memory:.1f} GB")

                # Suggest alternative models
                suitable_models = [model for model, mem in model_memory_requirements.items()
                                 if mem <= gpu_memory]
                if suitable_models:
                    print(f"💡 Recommended models for your GPU: {', '.join(suitable_models)}")
            else:
                print(f"✅ {self.model_size} model is compatible with your GPU ({gpu_memory:.1f} GB)")

    def get_device_info(self) -> dict:
        """Get detailed device information"""
        info = {
            'device': self.device,
            'compute_type': self.compute_type,
            'cuda_available': torch.cuda.is_available(),
            'gpu_count': 0,
            'gpu_name': None,
            'gpu_memory': 0
        }

        if torch.cuda.is_available():
            info['gpu_count'] = torch.cuda.device_count()
            if info['gpu_count'] > 0:
                info['gpu_name'] = torch.cuda.get_device_name(0)
                info['gpu_memory'] = torch.cuda.get_device_properties(0).total_memory / (1024**3)

        return info
    
    def set_model(self, model_size: str):
        """Change the Whisper model"""
        if model_size != self.model_size:
            self.model_size = model_size
            # Clear current model to force reload
            self.model = None
            print(f"Whisper model changed to: {model_size}")

            # Re-optimize settings for the new model
            self._optimize_settings()
            self._check_model_gpu_compatibility()
    
    def _load_model(self):
        """Load the Whisper model with optimized settings"""
        if self.model is not None:
            return  # Model already loaded

        try:
            print(f"Loading Whisper model: {self.model_size}")
            print(f"Device: {self.device}, Compute type: {self.compute_type}")

            # Show performance expectations
            self._show_performance_info()

            self.model = WhisperModel(
                self.model_size,
                device=self.device,
                compute_type=self.compute_type
            )
            print("✅ Model loaded successfully")

            # Show actual memory usage if on GPU
            if self.device == 'cuda' and torch.cuda.is_available():
                memory_used = torch.cuda.memory_allocated(0) / (1024**3)
                print(f"📊 GPU memory used: {memory_used:.2f} GB")

        except Exception as e:
            print(f"❌ Error loading model: {e}")
            # Fallback to CPU if GPU fails
            if self.device == 'cuda':
                print("🔄 Falling back to CPU...")
                self.device = 'cpu'
                self.compute_type = 'int8'
                try:
                    self.model = WhisperModel(self.model_size, device=self.device, compute_type=self.compute_type)
                    print("✅ Model loaded on CPU")
                except Exception as e2:
                    print(f"CPU fallback failed: {e2}")
                    # Ultimate fallback to base model on CPU
                    self.model_size = "base"
                    self.model = WhisperModel("base", device="cpu", compute_type="int8")
                    print("✅ Fallback to base model on CPU")
            else:
                # Fallback to base model if large model fails on CPU
                if self.model_size != "base":
                    print("🔄 Falling back to base model...")
                    self.model_size = "base"
                    try:
                        self.model = WhisperModel(self.model_size, device=self.device, compute_type=self.compute_type)
                        print("✅ Base model loaded")
                    except Exception as e2:
                        print(f"Base model fallback failed: {e2}")
                        # Ultimate fallback
                        self.model = WhisperModel("base", device="cpu", compute_type="int8")
                        print("✅ Ultimate fallback to base/CPU/int8")
                else:
                    raise e

    def _show_performance_info(self):
        """Show expected performance information"""
        if self.device == 'cuda':
            print("⚡ Expected performance: 5-10x faster than CPU")
            print("🎯 Optimal for real-time transcription")
        else:
            print("🐌 CPU transcription may be slower")
            self._show_gpu_upgrade_info()

    def _show_gpu_upgrade_info(self):
        """Show information about GPU upgrade options"""
        print("💡 For faster transcription, consider:")
        print("   1. NVIDIA GPU with CUDA support (GTX 1060+ or RTX series)")
        print("   2. Install CUDA-compatible PyTorch:")
        print("      pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
        print("   3. Ensure NVIDIA drivers and CUDA toolkit are installed")

    def get_cuda_info(self) -> dict:
        """Get detailed CUDA information for troubleshooting"""
        info = {
            'torch_version': torch.__version__,
            'cuda_available': torch.cuda.is_available(),
            'cuda_version': None,
            'cudnn_version': None,
            'gpu_count': 0,
            'gpus': []
        }

        if torch.cuda.is_available():
            info['cuda_version'] = torch.version.cuda
            info['cudnn_version'] = torch.backends.cudnn.version()
            info['gpu_count'] = torch.cuda.device_count()

            for i in range(info['gpu_count']):
                gpu_props = torch.cuda.get_device_properties(i)
                info['gpus'].append({
                    'name': gpu_props.name,
                    'memory': gpu_props.total_memory / (1024**3),
                    'compute_capability': f"{gpu_props.major}.{gpu_props.minor}"
                })

        return info
    
    def transcribe(self, audio_file=None, language=None):
        """
        Transcribe audio file to text
        
        Args:
            audio_file: Path to audio file (optional, uses self.audio_file if not provided)
            language: Language code for transcription (optional, auto-detect if None)
        
        Returns:
            str: Transcribed text
        """
        if audio_file:
            self.audio_file = audio_file
        
        if not self.audio_file:
            raise ValueError("No audio file specified")
        
        if not os.path.exists(self.audio_file):
            raise FileNotFoundError(f"Audio file not found: {self.audio_file}")
        
        # Convert audio to WAV format if needed (Whisper works best with WAV)
        wav_file = self._convert_to_wav(self.audio_file)
        
        try:
            print("Starting transcription...")
            
            # Load model if not already loaded
            self._load_model()
            
            # Transcribe audio
            segments, info = self.model.transcribe(
                wav_file,
                language=language,
                beam_size=5,
                best_of=5,
                temperature=0.0,
                condition_on_previous_text=True,
                vad_filter=True,  # Voice activity detection
                vad_parameters=dict(min_silence_duration_ms=1000)
            )
            
            print(f"Detected language: {info.language} (probability: {info.language_probability:.2f})")
            
            # Combine all segments into full transcription
            transcription = ""
            for segment in segments:
                transcription += segment.text + " "
            
            # Clean up temporary file if created
            if wav_file != self.audio_file:
                os.remove(wav_file)
            
            return transcription.strip()
            
        except Exception as e:
            # Clean up temporary file if created
            if wav_file != self.audio_file:
                try:
                    os.remove(wav_file)
                except:
                    pass
            raise Exception(f"Transcription failed: {str(e)}")
    
    def transcribe_with_timestamps(self, audio_file=None, language=None):
        """
        Transcribe audio file with timestamp information
        
        Args:
            audio_file: Path to audio file (optional, uses self.audio_file if not provided)
            language: Language code for transcription (optional, auto-detect if None)
        
        Returns:
            list: List of dictionaries with 'start', 'end', and 'text' keys
        """
        if audio_file:
            self.audio_file = audio_file
        
        if not self.audio_file:
            raise ValueError("No audio file specified")
        
        if not os.path.exists(self.audio_file):
            raise FileNotFoundError(f"Audio file not found: {self.audio_file}")
        
        # Convert audio to WAV format if needed
        wav_file = self._convert_to_wav(self.audio_file)
        
        try:
            print("Starting transcription with timestamps...")
            
            # Load model if not already loaded
            self._load_model()
            
            # Transcribe audio
            segments, info = self.model.transcribe(
                wav_file,
                language=language,
                beam_size=5,
                best_of=5,
                temperature=0.0,
                condition_on_previous_text=True,
                vad_filter=True,
                vad_parameters=dict(min_silence_duration_ms=1000)
            )
            
            print(f"Detected language: {info.language} (probability: {info.language_probability:.2f})")
            
            # Extract segments with timestamps
            timestamped_segments = []
            for segment in segments:
                timestamped_segments.append({
                    'start': segment.start,
                    'end': segment.end,
                    'text': segment.text.strip()
                })
            
            # Clean up temporary file if created
            if wav_file != self.audio_file:
                os.remove(wav_file)
            
            return timestamped_segments
            
        except Exception as e:
            # Clean up temporary file if created
            if wav_file != self.audio_file:
                try:
                    os.remove(wav_file)
                except:
                    pass
            raise Exception(f"Transcription failed: {str(e)}")
    
    def _convert_to_wav(self, audio_file):
        """
        Convert audio file to WAV format if needed
        
        Args:
            audio_file: Path to input audio file
        
        Returns:
            str: Path to WAV file (either original or converted)
        """
        # Check if file is already WAV
        if audio_file.lower().endswith('.wav'):
            return audio_file
        
        try:
            print(f"Converting {audio_file} to WAV format...")
            
            # Load audio file
            audio = AudioSegment.from_file(audio_file)
            
            # Create temporary WAV file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
                temp_wav_path = temp_file.name
            
            # Export as WAV
            audio.export(temp_wav_path, format="wav")
            
            print("Conversion completed")
            return temp_wav_path
            
        except Exception as e:
            raise Exception(f"Audio conversion failed: {str(e)}")
    
    def get_supported_formats(self):
        """Get list of supported audio formats"""
        return ['.wav', '.mp3', '.m4a', '.flac', '.ogg', '.mp4', '.avi', '.mov']
    
    def set_audio_file(self, file_path):
        """Set the audio file to be transcribed"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Audio file not found: {file_path}")
        self.audio_file = file_path
    
    def change_model(self, model_size):
        """Change the Whisper model size"""
        if model_size != self.model_size:
            self.model_size = model_size
            self._load_model()
