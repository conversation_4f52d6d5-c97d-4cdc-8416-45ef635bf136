@echo off
echo ðŸŽ™ï¸� AI Meeting Notes Application
echo ================================

:: Check if virtual environment exists and use it
if exist "venv\" (
    echo ðŸ”„ Using virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo âš ï¸�  No virtual environment found - using system Python
    echo ðŸ’¡ For isolated installation, run: setup_and_run.bat
    
    :: Check if Python is installed
    python --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo â�Œ Python is not installed or not in PATH
        echo Please install Python 3.8+ from https://python.org
        pause
        exit /b 1
    )
)

:: Check if main.py exists
if not exist "main.py" (
    echo â�Œ main.py not found
    echo Please run this from the Meeting directory
    pause
    exit /b 1
)

:: Run setup if first time
if not exist ".env" (
    echo ðŸ”§ First time setup...
    python setup.py
    echo.
    echo âš ï¸�  Please edit the .env file to add your OpenAI API key
    echo    The app will work with limited features without it
    echo.
    pause
)

:: Start the application
echo ðŸš€ Starting AI Meeting Notes...
python main.py

:: Keep window open if there's an error
if %errorlevel% neq 0 (
    echo.
    echo â�Œ Application ended with error code %errorlevel%
    pause
)
