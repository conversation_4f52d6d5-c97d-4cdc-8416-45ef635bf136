@echo off
setlocal enabledelayedexpansion

echo.
echo ========================================
echo ??? AI Meeting Notes - Smart Startup
echo ========================================
echo.

:: Check if virtual environment exists
if exist "venv\" (
    echo ?? Activating virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo ??  No virtual environment found
    echo Please run setup_and_run.bat first
    pause
    exit /b 1
)

:: Quick GPU detection
echo ?? Checking your system capabilities...
echo.
python quick_gpu_check.py

echo.
echo ========================================
echo ?? Starting Application
echo ========================================
echo.

:: Start the main application
python main.py

:: Handle errors
if %errorlevel% neq 0 (
    echo.
    echo ? Application ended with error
    echo.
    echo ?? Troubleshooting:
    echo   - Check if all dependencies are installed
    echo   - Run setup_and_run.bat to reinstall
    echo   - Check detect_gpu.bat for system info
    echo.
    pause
)
