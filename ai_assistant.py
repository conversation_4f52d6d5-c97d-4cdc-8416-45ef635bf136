import openai
import os
from dotenv import load_dotenv
import json
import re
import requests
import time
from typing import Optional, Dict, Any

# Load environment variables
load_dotenv()

class AIAssistant:
    def __init__(self):
        """
        Initialize the AI Assistant with multiple LLM provider support
        """
        # Load configuration
        self.provider = os.getenv('LLM_PROVIDER', 'openai').lower()
        self.model = os.getenv('LLM_MODEL', 'gpt-3.5-turbo')
        
        # Initialize provider-specific settings
        self._init_provider_config()
        
        print(f"AI Assistant initialized with provider: {self.provider}")
        if not self.api_available:
            print("Warning: No API configuration found. Using local fallback responses")
    
    def _init_provider_config(self):
        """Initialize configuration for different LLM providers"""
        self.api_available = False
        
        if self.provider == 'openai':
            self.api_key = os.getenv('OPENAI_API_KEY')
            if self.api_key:
                openai.api_key = self.api_key
                self.api_available = True
                self.model = self.model or os.getenv('OPENAI_MODEL', 'gpt-4o-mini')

        elif self.provider == 'gemini':
            self.api_key = os.getenv('GEMINI_API_KEY')
            if self.api_key:
                self.api_available = True
                self.model = self.model or os.getenv('GEMINI_MODEL', 'gemini-2.5-flash')
                self.base_url = 'https://generativelanguage.googleapis.com/v1beta'

        elif self.provider == 'groq':
            self.api_key = os.getenv('GROQ_API_KEY')
            if self.api_key:
                self.api_available = True
                self.model = self.model or os.getenv('GROQ_MODEL', 'llama-3.3-70b-versatile')
                self.base_url = 'https://api.groq.com/openai/v1'

        elif self.provider == 'anthropic':
            self.api_key = os.getenv('ANTHROPIC_API_KEY')
            if self.api_key:
                self.api_available = True
                self.model = self.model or os.getenv('ANTHROPIC_MODEL', 'claude-3-5-haiku-20241022')
        
        elif self.provider == 'ollama':
            self.base_url = os.getenv('OLLAMA_URL', 'http://localhost:11434')
            self.model = self.model or os.getenv('OLLAMA_MODEL', 'llama3.2')
            # Check if Ollama is running
            if self._check_ollama_connection():
                self.api_available = True

        elif self.provider == 'lmstudio':
            self.base_url = os.getenv('LMSTUDIO_URL', 'http://localhost:1234')
            self.model = self.model or os.getenv('LMSTUDIO_MODEL', 'local-model')
            # Check if LM Studio is running
            if self._check_lmstudio_connection():
                self.api_available = True

        elif self.provider == 'cohere':
            self.api_key = os.getenv('COHERE_API_KEY')
            if self.api_key:
                self.api_available = True
                self.model = self.model or os.getenv('COHERE_MODEL', 'command-light')
    
    def _check_ollama_connection(self) -> bool:
        """Check if Ollama is running and accessible"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def _check_lmstudio_connection(self) -> bool:
        """Check if LM Studio is running and accessible"""
        try:
            response = requests.get(f"{self.base_url}/v1/models", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def ask_question(self, transcription, question):
        """
        Ask a specific question about the meeting transcription
        
        Args:
            transcription (str): The meeting transcription
            question (str): The user's question
        
        Returns:
            str: The AI's response to the question
        """
        if not transcription.strip():
            return "No transcription available to analyze."
        
        # If no API available, use local fallback
        if not self.api_available:
            return self._local_question_fallback(transcription, question)
        
        try:
            prompt = f"""
You are an AI assistant analyzing a meeting transcription. Please answer the user's question based on the transcription provided.

Meeting Transcription:
{transcription}

User Question: {question}

Please provide a clear, accurate answer based only on the information available in the transcription. If the information isn't available in the transcription, please say so.
"""
            
            response = self._call_llm_api(prompt, max_tokens=500, temperature=0.3)
            return response
            
        except Exception as e:
            return f"Error generating response: {str(e)}\n\nFallback: {self._local_question_fallback(transcription, question)}"
    
    def generate_summary(self, transcription):
        """
        Generate a dynamic, detailed summary of the meeting
        
        Args:
            transcription (str): The meeting transcription
        
        Returns:
            str: A comprehensive, dynamic summary
        """
        if not transcription.strip():
            return "No transcription available to summarize."
        
        # If no API available, use local fallback
        if not self.api_available:
            return self._local_summary_fallback(transcription)
        
        try:
            prompt = f"""
Analyze the following meeting transcription and create a comprehensive, detailed summary. The summary should be dynamic and adapt to the content of the meeting. Do not use a template format.

Meeting Transcription:
{transcription}

Please create a summary that includes:

1. **Main Topics and Discussion Points**: Identify and elaborate on the key subjects discussed
2. **Key Decisions Made**: List any concrete decisions or resolutions
3. **Action Items and Next Steps**: Extract specific tasks, assignments, and follow-up actions
4. **Important Details and Context**: Include relevant background information, data points, or context mentioned
5. **Participants and Roles**: Identify speakers and their contributions (if identifiable)
6. **Timeline and Deadlines**: Note any dates, timelines, or deadlines mentioned
7. **Concerns or Issues Raised**: Highlight any problems, challenges, or concerns discussed
8. **Outcomes and Agreements**: Summarize what was agreed upon or resolved

Make the summary detailed but well-organized. Adapt the structure and content based on what was actually discussed in the meeting. If certain sections don't apply to this particular meeting, focus more on the relevant aspects.
"""
            
            response = self._call_llm_api(prompt, max_tokens=1000, temperature=0.2)
            return response
            
        except Exception as e:
            return f"Error generating summary: {str(e)}\n\n{self._local_summary_fallback(transcription)}"
    
    def extract_action_items(self, transcription):
        """
        Extract specific action items from the transcription
        
        Args:
            transcription (str): The meeting transcription
        
        Returns:
            list: List of action items
        """
        if not self.api_key:
            return self._extract_action_items_local(transcription)
        
        try:
            prompt = f"""
Analyze the following meeting transcription and extract all action items, tasks, and next steps mentioned.

Meeting Transcription:
{transcription}

Please list each action item in the following format:
- [Action Item] - [Assigned to (if mentioned)] - [Deadline (if mentioned)]

If no specific person or deadline is mentioned, just list the action item.
"""
            
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert at extracting action items and next steps from meeting discussions."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=400,
                temperature=0.1
            )
            
            action_items = response.choices[0].message.content.strip().split('\n')
            return [item.strip() for item in action_items if item.strip() and item.strip().startswith('-')]
            
        except Exception as e:
            return self._extract_action_items_local(transcription)
    
    def _local_question_fallback(self, transcription, question):
        """
        Local fallback for question answering when API is not available
        """
        question_lower = question.lower()
        transcription_lower = transcription.lower()
        
        # Simple keyword-based responses
        if any(word in question_lower for word in ['who', 'participants', 'attendees']):
            return "To identify participants, please review the transcription for speaker names or introductions."
        
        elif any(word in question_lower for word in ['when', 'time', 'date']):
            # Look for time-related information
            time_patterns = re.findall(r'\b\d{1,2}:\d{2}\b|\b\d{1,2}/\d{1,2}/\d{4}\b|\b\w+day\b', transcription)
            if time_patterns:
                return f"Time-related information found: {', '.join(time_patterns[:3])}"
            return "No specific time or date information clearly identified in the transcription."
        
        elif any(word in question_lower for word in ['action', 'task', 'next steps', 'follow up']):
            # Look for action-related keywords
            action_keywords = ['will', 'should', 'need to', 'action', 'task', 'follow up', 'next']
            found_actions = []
            sentences = transcription.split('.')
            for sentence in sentences:
                if any(keyword in sentence.lower() for keyword in action_keywords):
                    found_actions.append(sentence.strip())
            
            if found_actions:
                return f"Potential action items found:\n" + "\n".join(found_actions[:3])
            return "No clear action items identified in the transcription."
        
        elif any(word in question_lower for word in ['decision', 'decided', 'agreement', 'agreed']):
            # Look for decision-related keywords
            decision_keywords = ['decided', 'decision', 'agreed', 'agreement', 'conclude', 'final']
            found_decisions = []
            sentences = transcription.split('.')
            for sentence in sentences:
                if any(keyword in sentence.lower() for keyword in decision_keywords):
                    found_decisions.append(sentence.strip())
            
            if found_decisions:
                return f"Potential decisions found:\n" + "\n".join(found_decisions[:3])
            return "No clear decisions identified in the transcription."
        
        else:
            # General search
            question_words = question_lower.split()
            relevant_sentences = []
            sentences = transcription.split('.')
            
            for sentence in sentences:
                if any(word in sentence.lower() for word in question_words if len(word) > 3):
                    relevant_sentences.append(sentence.strip())
            
            if relevant_sentences:
                return f"Relevant information found:\n" + "\n".join(relevant_sentences[:3])
            return "I couldn't find specific information related to your question in the transcription. Please review the full transcription or rephrase your question."
    
    def _local_summary_fallback(self, transcription):
        """
        Local fallback for summary generation when API is not available
        """
        words = transcription.split()
        word_count = len(words)
        
        # Extract key information
        sentences = transcription.split('.')
        key_sentences = []
        
        # Look for important keywords
        important_keywords = [
            'decision', 'decided', 'agree', 'agreed', 'conclusion',
            'action', 'task', 'next', 'follow up', 'deadline',
            'important', 'critical', 'urgent', 'priority',
            'budget', 'cost', 'price', 'timeline', 'schedule'
        ]
        
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence and any(keyword in sentence.lower() for keyword in important_keywords):
                key_sentences.append(sentence)
        
        # Generate basic summary
        summary = f"**Meeting Summary** (Generated locally)\n\n"
        summary += f"**Transcription Length**: {word_count} words\n\n"
        
        if key_sentences:
            summary += "**Key Points Identified**:\n"
            for i, sentence in enumerate(key_sentences[:5], 1):
                summary += f"{i}. {sentence}\n"
        else:
            summary += "**General Overview**:\n"
            summary += f"The meeting transcription contains {word_count} words. "
            summary += "For a detailed analysis, please review the full transcription or set up an OpenAI API key for AI-powered summaries.\n"
        
        summary += "\n**Note**: This is a basic local summary. For comprehensive AI analysis, please configure an OpenAI API key in your .env file."
        
        return summary
    
    def _extract_action_items_local(self, transcription):
        """
        Local fallback for action item extraction
        """
        action_items = []
        sentences = transcription.split('.')
        
        action_keywords = ['will', 'should', 'need to', 'must', 'action', 'task', 'follow up', 'next step']
        
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence and any(keyword in sentence.lower() for keyword in action_keywords):
                action_items.append(f"- {sentence}")
        
        return action_items[:10]  # Limit to first 10 items
    
    def _call_llm_api(self, prompt: str, max_tokens: int = 500, temperature: float = 0.3) -> str:
        """Unified method to call different LLM APIs"""
        
        if self.provider == 'openai':
            return self._call_openai_api(prompt, max_tokens, temperature)
        elif self.provider == 'gemini':
            return self._call_gemini_api(prompt, max_tokens, temperature)
        elif self.provider == 'groq':
            return self._call_groq_api(prompt, max_tokens, temperature)
        elif self.provider == 'anthropic':
            return self._call_anthropic_api(prompt, max_tokens, temperature)
        elif self.provider == 'ollama':
            return self._call_ollama_api(prompt, max_tokens, temperature)
        elif self.provider == 'lmstudio':
            return self._call_lmstudio_api(prompt, max_tokens, temperature)
        elif self.provider == 'cohere':
            return self._call_cohere_api(prompt, max_tokens, temperature)
        else:
            raise ValueError(f"Unsupported provider: {self.provider}")
    
    def _call_openai_api(self, prompt: str, max_tokens: int, temperature: float) -> str:
        """Call OpenAI API"""
        response = openai.ChatCompletion.create(
            model=self.model,
            messages=[
                {"role": "system", "content": "You are a helpful AI assistant that analyzes meeting transcriptions."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=max_tokens,
            temperature=temperature
        )
        return response.choices[0].message.content.strip()
    
    def _call_gemini_api(self, prompt: str, max_tokens: int, temperature: float) -> str:
        """Call Google Gemini API"""
        url = f"{self.base_url}/models/{self.model}:generateContent?key={self.api_key}"
        
        payload = {
            "contents": [{
                "parts": [{"text": prompt}]
            }],
            "generationConfig": {
                "temperature": temperature,
                "maxOutputTokens": max_tokens
            }
        }
        
        response = requests.post(url, json=payload, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        if 'candidates' in result and len(result['candidates']) > 0:
            return result['candidates'][0]['content']['parts'][0]['text']
        else:
            raise Exception("No response from Gemini API")
    
    def _call_groq_api(self, prompt: str, max_tokens: int, temperature: float) -> str:
        """Call Groq API (OpenAI compatible)"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": [
                {"role": "system", "content": "You are a helpful AI assistant that analyzes meeting transcriptions."},
                {"role": "user", "content": prompt}
            ],
            "max_tokens": max_tokens,
            "temperature": temperature
        }
        
        response = requests.post(f"{self.base_url}/chat/completions", 
                               headers=headers, json=payload, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        return result['choices'][0]['message']['content']
    
    def _call_anthropic_api(self, prompt: str, max_tokens: int, temperature: float) -> str:
        """Call Anthropic Claude API"""
        try:
            import anthropic
        except ImportError:
            raise Exception("anthropic package not installed. Run: pip install anthropic")
        
        client = anthropic.Anthropic(api_key=self.api_key)
        
        response = client.messages.create(
            model=self.model,
            max_tokens=max_tokens,
            temperature=temperature,
            messages=[
                {"role": "user", "content": prompt}
            ]
        )
        
        return response.content[0].text
    
    def _call_ollama_api(self, prompt: str, max_tokens: int, temperature: float) -> str:
        """Call Ollama local API"""
        payload = {
            "model": self.model,
            "prompt": prompt,
            "options": {
                "temperature": temperature,
                "num_predict": max_tokens
            },
            "stream": False
        }
        
        response = requests.post(f"{self.base_url}/api/generate", 
                               json=payload, timeout=60)
        response.raise_for_status()
        
        result = response.json()
        return result['response']
    
    def _call_lmstudio_api(self, prompt: str, max_tokens: int, temperature: float) -> str:
        """Call LM Studio local API (OpenAI compatible)"""
        headers = {"Content-Type": "application/json"}
        
        payload = {
            "model": self.model,
            "messages": [
                {"role": "system", "content": "You are a helpful AI assistant that analyzes meeting transcriptions."},
                {"role": "user", "content": prompt}
            ],
            "max_tokens": max_tokens,
            "temperature": temperature
        }
        
        response = requests.post(f"{self.base_url}/v1/chat/completions", 
                               headers=headers, json=payload, timeout=60)
        response.raise_for_status()
        
        result = response.json()
        return result['choices'][0]['message']['content']
    
    def _call_cohere_api(self, prompt: str, max_tokens: int, temperature: float) -> str:
        """Call Cohere API"""
        try:
            import cohere
        except ImportError:
            raise Exception("cohere package not installed. Run: pip install cohere")
        
        client = cohere.Client(api_key=self.api_key)
        
        response = client.generate(
            model=self.model,
            prompt=prompt,
            max_tokens=max_tokens,
            temperature=temperature
        )
        
        return response.generations[0].text
    
    def get_available_models(self) -> list:
        """Get list of available models for the current provider"""
        try:
            # First try to get models from .env configuration
            env_models = self._get_models_from_env()
            if env_models:
                return env_models
            
            # For local providers, try to connect and get actual models
            if self.provider == 'ollama':
                try:
                    response = requests.get(f"{self.base_url}/api/tags", timeout=5)
                    if response.status_code == 200:
                        models = response.json().get('models', [])
                        installed_models = [model['name'].split(':')[0] for model in models]  # Remove tags
                        if installed_models:
                            print(f"✅ Found {len(installed_models)} Ollama models: {', '.join(installed_models[:3])}{'...' if len(installed_models) > 3 else ''}")
                            return installed_models
                        else:
                            print("⚠️ Ollama is running but no models are installed")
                    else:
                        print(f"❌ Ollama API returned status {response.status_code}")
                except requests.exceptions.ConnectionError:
                    print("❌ Cannot connect to Ollama - please start Ollama service")
                except requests.exceptions.Timeout:
                    print("❌ Ollama connection timeout - service may be slow")
                except Exception as e:
                    print(f"❌ Ollama error: {e}")

                # Return .env models as fallback
                fallback_models = self._get_models_from_env() or ['llama3.2', 'llama3.1', 'mistral']
                print(f"📋 Using fallback models from .env: {', '.join(fallback_models)}")
                return fallback_models

            elif self.provider == 'lmstudio':
                try:
                    response = requests.get(f"{self.base_url}/v1/models", timeout=5)
                    if response.status_code == 200:
                        models = response.json().get('data', [])
                        loaded_models = [model['id'] for model in models]
                        if loaded_models:
                            print(f"✅ Found {len(loaded_models)} LM Studio models: {', '.join(loaded_models[:3])}{'...' if len(loaded_models) > 3 else ''}")
                            return loaded_models
                        else:
                            print("⚠️ LM Studio is running but no models are loaded")
                    else:
                        print(f"❌ LM Studio API returned status {response.status_code}")
                except requests.exceptions.ConnectionError:
                    print("❌ Cannot connect to LM Studio - please start LM Studio server")
                except requests.exceptions.Timeout:
                    print("❌ LM Studio connection timeout - service may be slow")
                except Exception as e:
                    print(f"❌ LM Studio error: {e}")

                # Return .env models as fallback
                fallback_models = self._get_models_from_env() or ['local-model']
                print(f"📋 Using fallback models from .env: {', '.join(fallback_models)}")
                return fallback_models
            
            # For cloud providers, return .env models or fallback
            return env_models or [self.model]
                
        except Exception as e:
            print(f"Error getting models: {e}")
        
        return [self.model]  # Return current model as final fallback
    
    def _get_models_from_env(self) -> list:
        """Get model list from .env configuration"""
        env_key = f"{self.provider.upper()}_MODELS"
        models_str = os.getenv(env_key, '')
        if models_str:
            return [model.strip() for model in models_str.split(',') if model.strip()]
        return []
    
    def set_provider(self, provider: str, model: str = None):
        """Change the LLM provider and optionally the model"""
        self.provider = provider.lower()
        if model:
            self.model = model
        self._init_provider_config()
    
    def set_model(self, model_name):
        """
        Change the AI model being used

        Args:
            model_name (str): Name of the model to use
        """
        self.model = model_name

    def reload_env_config(self):
        """Reload configuration from .env file"""
        load_dotenv(override=True)  # Reload with override
        self._init_provider_config()
        print("🔄 Configuration reloaded from .env file")
