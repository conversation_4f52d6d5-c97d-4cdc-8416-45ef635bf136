#!/usr/bin/env python3
"""
Quick GPU/CPU detection script for AI Meeting Notes
Shows system capabilities and optimal settings
"""

import torch
import sys

def check_gpu_capabilities():
    """Check and display GPU/CPU capabilities"""
    
    print("🔍 Quick GPU/CPU Detection")
    print("=" * 30)
    
    # Basic system info
    print(f"PyTorch: {torch.__version__}")
    
    if torch.cuda.is_available():
        # GPU detected
        gpu_count = torch.cuda.device_count()
        print(f"🚀 NVIDIA GPU: ✅ ({gpu_count} device{'s' if gpu_count > 1 else ''})")
        
        # Get primary GPU info
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        
        print(f"GPU: {gpu_name}")
        print(f"Memory: {gpu_memory:.1f} GB")
        print(f"CUDA: {torch.version.cuda}")
        
        # Performance expectations
        print("\n⚡ Performance:")
        print("  Speed: 5-10x faster than CPU")
        print("  Quality: Excellent")
        
        # Model recommendations
        print("\n🎯 Recommended Settings:")
        if gpu_memory >= 8:
            print("  Model: large-v3 (best quality)")
            print("  Device: CUDA")
            print("  Type: float16")
        elif gpu_memory >= 4:
            print("  Model: large-v2 or medium")
            print("  Device: CUDA") 
            print("  Type: float16")
        elif gpu_memory >= 2:
            print("  Model: medium or small")
            print("  Device: CUDA")
            print("  Type: float16")
        else:
            print("  Model: small or base")
            print("  Device: CUDA")
            print("  Type: float16")
            print("  ⚠️  Limited by GPU memory")
            
        print("\n✅ Your system is optimized for fast transcription!")
        
    else:
        # CPU only
        print("🖥️  NVIDIA GPU: ❌ (CPU only)")
        
        print("\n🐌 Performance:")
        print("  Speed: Standard CPU processing")
        print("  Quality: Good")
        
        print("\n🎯 Recommended Settings:")
        print("  Model: medium or small")
        print("  Device: CPU")
        print("  Type: int8")
        
        print("\n💡 To enable GPU acceleration:")
        print("  1. Install NVIDIA GPU (GTX 1060+ or RTX)")
        print("  2. Install NVIDIA drivers")
        print("  3. Install CUDA toolkit")
        print("  4. Install CUDA PyTorch")
        
        print("\nℹ️  CPU transcription works fine, just slower!")

def main():
    """Main function"""
    try:
        check_gpu_capabilities()
        return 0
    except Exception as e:
        print(f"❌ Error checking GPU: {e}")
        print("🔧 This might indicate a PyTorch installation issue")
        return 1

if __name__ == "__main__":
    sys.exit(main())
