# 🎙️ AI Meeting Notes - Voice Recorder & Transcription App

A comprehensive desktop application for recording meetings from any video call platform, transcribing audio using state-of-the-art AI, and generating intelligent summaries and insights.

## ✨ Features

### 🎤 Audio Recording
- **Real-time recording** from any microphone or system audio
- **Cross-platform compatibility** with Windows, macOS, and Linux
- **High-quality audio capture** with configurable sample rates
- **Automatic file management** with timestamped recordings

### 📁 File Upload Support
- Support for multiple audio formats: **WAV, MP3, M4A, FLAC, OGG, MP4**
- **Drag-and-drop interface** for easy file uploads
- **Automatic format conversion** for optimal transcription

### 🤖 AI-Powered Transcription
- **Faster Whisper XXL** model for high-accuracy transcription
- **🚀 NVIDIA GPU acceleration** - automatically detects and uses CUDA for 5-10x faster processing
- **Smart device optimization** - automatically selects best settings for your hardware
- **Automatic language detection** with 99+ language support
- **Speaker diarization** and timestamp extraction
- **Offline processing** - no data leaves your computer for transcription

### 🧠 Intelligent AI Assistant
- **Dynamic question answering** about meeting content
- **Context-aware responses** based on transcription
- **Smart summary generation** - adapts to meeting content (not templated)
- **Action item extraction** and deadline identification
- **Participant identification** and role analysis

### 📊 Export Capabilities
- **PDF Export** - Professional formatted reports
- **Word Document** - Editable .docx files with structured layout
- **Plain Text** - Simple .txt files for universal compatibility
- **JSON Export** - Machine-readable format for integration

### 💾 Meeting Management
- **Persistent storage** of all meeting data
- **Search and filter** previous meetings
- **Meeting history** with quick access
- **Metadata management** (participants, dates, titles)

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- Windows 10/11 (current focus - macOS/Linux support coming)
- Microphone access for recording
- 4GB+ RAM (for Whisper model)

### Installation

1. **Clone or download the project**
```bash
git clone <repository-url>
cd Meeting
```

2. **Run the setup script**
```bash
python setup.py
```

3. **Configure OpenAI API (Optional)**
   - Edit the `.env` file
   - Add your OpenAI API key from [platform.openai.com](https://platform.openai.com/api-keys)
   ```
   OPENAI_API_KEY=your_api_key_here
   ```

4. **Launch the application**
```bash
python main.py
```

## 📖 Usage Guide

### Recording a Meeting

1. **Start Recording**
   - Click the "🔴 Start Recording" button
   - The app will request microphone permissions
   - Recording timer will show elapsed time

2. **Add Meeting Information**
   - Fill in meeting title, date, and participants
   - This metadata helps with organization and summaries

3. **Stop Recording**
   - Click "⏹️ Stop Recording" when finished
   - Audio file is automatically saved with timestamp

### Uploading Audio Files

1. **Click "📁 Upload Audio File"**
2. **Select your audio file** (supports most formats)
3. **File is automatically processed** and ready for transcription

### 🔍 GPU Detection & Optimization

**Before first use, check your system capabilities:**

```bash
# Comprehensive GPU/CPU analysis
detect_gpu.bat

# Quick check with app startup
start_with_gpu_check.bat
```

**The app automatically:**
- Detects NVIDIA GPU with CUDA support
- Optimizes settings for your hardware
- Recommends best Whisper models
- Shows performance expectations

### Transcription Process

1. **Select optimal Whisper model** (dropdown shows recommendations)
2. **Click "🔤 Transcribe Audio"**
3. **Wait for processing** (first run downloads Whisper model ~3GB)
   - 🚀 **With GPU**: 5-10x faster processing
   - 🖥️ **With CPU**: Standard speed, still excellent quality
4. **Review transcription** in the right panel
5. **Edit if needed** - transcription is editable

### AI Features

#### Asking Questions
1. **Type your question** in the question box
2. **Click "❓ Ask Question"**
3. **Get contextual answers** based on the meeting content

Example questions:
- "What decisions were made in this meeting?"
- "Who was assigned to work on the project?"
- "What are the next steps?"
- "When is the deadline for the proposal?"

#### Generating Summaries
1. **Click "📝 Generate Summary"**
2. **AI analyzes the full transcription**
3. **Dynamic summary generated** - adapts to your meeting's content
4. **Review and edit** the summary as needed

### Exporting Meeting Notes

1. **Choose export format**:
   - **PDF** - Professional reports with formatting
   - **Word** - Editable documents with tables and structure
   - **Text** - Simple format for universal compatibility

2. **Files saved to exports folder** with timestamp and meeting title

## ⚙️ Configuration

### Whisper Model Settings
Edit transcription_service.py to change model size:
```python
# Options: tiny, base, small, medium, large-v1, large-v2, large-v3
model_size = "large-v3"  # Best quality but slower
model_size = "base"      # Faster but lower accuracy
```

### Audio Recording Settings
Edit audio_recorder.py for custom settings:
```python
sample_rate = 44100  # CD quality
channels = 1         # Mono recording
```

### AI Model Settings
Edit ai_assistant.py to change OpenAI model:
```python
self.model = "gpt-3.5-turbo"  # Default
self.model = "gpt-4"          # Higher quality (requires API access)
```

## 🏗️ Architecture

### Project Structure
```
Meeting/
├── main.py                 # Main application GUI
├── audio_recorder.py       # Audio recording functionality
├── transcription_service.py # Whisper transcription
├── ai_assistant.py         # OpenAI integration
├── export_service.py       # File export features
├── setup.py               # Installation script
├── requirements.txt       # Dependencies
├── .env.example          # Configuration template
├── recordings/           # Recorded audio files
├── meetings/            # Meeting data storage
└── exports/            # Exported documents
```

### Key Components

#### AudioRecorder
- Real-time audio capture using `sounddevice`
- Multi-threaded recording for UI responsiveness
- Automatic file management and naming

#### TranscriptionService
- Faster Whisper integration for offline transcription
- Multi-format audio support with conversion
- Configurable model sizes for speed/accuracy tradeoff

#### AIAssistant
- OpenAI API integration for advanced features
- Local fallback for basic functionality without API
- Context-aware question answering

#### ExportService
- Multi-format document generation
- Professional formatting and layout
- Metadata inclusion and organization

## 🔧 Troubleshooting

### Common Issues

#### PyAudio Installation Errors
**Windows:**
```bash
pip install pipwin
pipwin install pyaudio
```
Or download wheel from [unofficial binaries](https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio)

#### Whisper Model Download
- First transcription downloads ~3GB model
- Ensure stable internet connection
- Models cached locally after first download

#### Microphone Access
- Grant microphone permissions when prompted
- Check Windows Privacy Settings > Microphone
- Ensure no other apps are using microphone exclusively

#### OpenAI API Issues
- Verify API key is correct in .env file
- Check API quota and billing
- App works with limited features without API key

### Performance Optimization

#### For Faster Transcription
- Use smaller Whisper models (base, small)
- Close other applications during processing
- Consider GPU acceleration (CUDA) for NVIDIA cards

#### For Better Accuracy
- Use larger Whisper models (large-v3)
- Ensure good audio quality (minimal background noise)
- Use appropriate microphone positioning

## 🛣️ Roadmap

### Version 2.0 (Mobile Development Ready)
- [ ] **Modular architecture** for mobile porting
- [ ] **Cloud sync** capabilities
- [ ] **Real-time transcription** during recording
- [ ] **Speaker identification** and labeling

### Version 1.5 (Enhanced Desktop)
- [ ] **macOS and Linux** support
- [ ] **Multiple language** UI
- [ ] **Advanced search** through meeting history
- [ ] **Integration** with calendar apps
- [ ] **Batch processing** for multiple files

### Mobile Development (Future)
- [ ] **React Native** or **Flutter** implementation
- [ ] **Cross-platform** audio recording
- [ ] **Cloud-based transcription** options
- [ ] **Mobile-optimized** UI/UX

## 🤝 Contributing

Contributions are welcome! Please feel free to submit issues and enhancement requests.

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **OpenAI Whisper** team for the amazing transcription models
- **Faster Whisper** developers for the optimized implementation
- **CustomTkinter** for the modern GUI framework
- **OpenAI** for the GPT models and API

## 💬 Support

For support, questions, or feature requests:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the documentation

---

**Built with ❤️ for better meeting productivity**
